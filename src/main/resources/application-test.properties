spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop

# Disable AWS Parameter Store for tests
aws.paramstore.enabled=false
spring.cloud.aws.paramstore.enabled=false

# CORS Domains
management.endpoints.web.cors.allowed-origins=https://test.nymbl.live/,https://test2.nymbl.live/

#TODO: Move this to parameter store
# LMN PDF Generator Lambda Function
lmn.pdf.lambda.function=arn:aws:lambda:us-east-2:086881067392:function:lmn-pdf-generator-test-function
