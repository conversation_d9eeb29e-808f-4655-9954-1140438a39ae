package com.nymbl.config.x12.x837;

import com.imsweb.x12.Loop;
import com.imsweb.x12.reader.X12Reader;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive validation tests for bulk X12 claim generation using com.imsweb:x12-parser library.
 * This test validates that bulk X12 files are 100% valid according to professional X12 standards.
 * 
 * This test does NOT require Spring context - it's a pure X12 validation test.
 */
public class BulkX12ValidationTest {

    /**
     * Test validates the x12.txt file in the project root using com.imsweb X12 parser.
     * This ensures the bulk X12 generation creates valid files with no warnings or errors.
     * Provides detailed diagnostic information about any issues found.
     */
    @Test
    public void testValidateX12FileWithImsweb() throws IOException {
        // Read the x12.txt file from project root
        String x12Content = Files.readString(Paths.get("x12.txt"));

        assertNotNull(x12Content, "X12 file content should not be null");
        assertFalse(x12Content.trim().isEmpty(), "X12 file content should not be empty");

        // Validate using com.imsweb X12 parser
        try {
            // Write content to a temporary file since X12Reader expects a File
            File tempFile = File.createTempFile("x12_test", ".txt");
            tempFile.deleteOnExit();
            Files.write(tempFile.toPath(), x12Content.getBytes());

            // Use the constructor shown in the GitHub documentation
            X12Reader x12Reader = new X12Reader(X12Reader.FileType.ANSI837_5010_X222, tempFile);

            // Parse the X12 file
            List<Loop> loops = x12Reader.getLoops();

            // Check for any parsing errors
            List<String> errors = x12Reader.getErrors();
            List<String> fatalErrors = x12Reader.getFatalErrors();

            // Print first 2000 characters of X12 content for debugging
            System.out.println("X12 Content (first 2000 chars):");
            System.out.println(x12Content.substring(0, Math.min(2000, x12Content.length())));
            System.out.println("...");

            // Just report what the parser found
            System.out.println("X12 Parser Results:");
            System.out.println("Loops parsed: " + (loops != null ? loops.size() : 0));
            System.out.println("Errors: " + errors.size());
            System.out.println("Fatal errors: " + fatalErrors.size());

            if (!errors.isEmpty()) {
                System.out.println("\nErrors found:");
                for (int i = 0; i < errors.size(); i++) {
                    System.out.println((i + 1) + ". " + errors.get(i));
                }
            }

            if (!fatalErrors.isEmpty()) {
                System.out.println("\nFatal errors found:");
                for (int i = 0; i < fatalErrors.size(); i++) {
                    System.out.println((i + 1) + ". " + fatalErrors.get(i));
                }
            }

            // Fail test if there are any errors
            if (!errors.isEmpty() || !fatalErrors.isEmpty()) {
                fail("X12 validation found " + errors.size() + " errors and " + fatalErrors.size() + " fatal errors.");
            }

        } catch (Exception e) {
            fail("Failed to parse X12 file: " + e.getMessage(), e);
        }
    }



}