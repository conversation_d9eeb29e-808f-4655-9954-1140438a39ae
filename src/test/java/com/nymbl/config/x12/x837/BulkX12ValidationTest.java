package com.nymbl.config.x12.x837;

import com.imsweb.x12.Loop;
import com.imsweb.x12.reader.X12Reader;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive validation tests for bulk X12 claim generation using com.imsweb:x12-parser library.
 * This test automatically generates bulk X12 claims using the same code path as the UI and validates them.
 * No manual file generation required - this test does everything automatically.
 */
@SpringBootTest(
    properties = {
        "aws.paramstore.enabled=false",
        "cloud.aws.stack.auto=false",
        "cloud.aws.region.auto=false",
        "cloud.aws.credentials.access-key=",
        "cloud.aws.credentials.secret-key=",
        "spring.profiles.active=test"
    },
    scanBasePackages = {
        "com.nymbl.config.x12",
        "com.nymbl.tenant.service",
        "com.nymbl.config.utils"
    }
)
public class BulkX12ValidationTest {

    @Autowired
    private Factory837 factory837;

    /**
     * Test that automatically generates bulk X12 claims and validates them using com.imsweb X12 parser.
     * This uses the EXACT same code path as the UI bulk claim generation.
     */
    @Test
    public void testGenerateAndValidateBulkX12Claims() throws Exception {
        // Use test claim IDs - these should exist in your test database
        List<Long> testClaimIds = Arrays.asList(1L, 2L); // Adjust these IDs based on your test data
        String timestamp = "20250101120000000";
        Long billingBranchId = 1L; // Adjust based on your test data

        System.out.println("=== Generating Bulk X12 Claims ===");
        System.out.println("Claim IDs: " + testClaimIds);
        System.out.println("Timestamp: " + timestamp);
        System.out.println("Billing Branch ID: " + billingBranchId);

        try {
            // Step 1: Build parameters for all claims (same as ClaimService.processBulkClaimJob)
            List<Factory837Parameters> paramsList = factory837.buildBulk(testClaimIds, timestamp, billingBranchId);
            assertNotNull(paramsList, "Parameters list should not be null");
            assertFalse(paramsList.isEmpty(), "Parameters list should not be empty");
            assertEquals(testClaimIds.size(), paramsList.size(), "Should have parameters for each claim");

            System.out.println("Built parameters for " + paramsList.size() + " claims");

            // Step 2: Check for validation errors (skip for now, focus on getting X12 content)
            System.out.println("Parameters built successfully");

            // Step 3: Create the bulk X12 claim (same as ClaimService.processBulkClaimJob)
            X12Claim bulkX12Claim = factory837.createBulkX12Claim(paramsList);
            assertNotNull(bulkX12Claim, "Bulk X12 claim should not be null");

            // Step 4: Generate X12 string (same as ClaimService.processBulkClaimJob)
            String x12Content = bulkX12Claim.toX12String();
            assertNotNull(x12Content, "X12 content should not be null");
            assertFalse(x12Content.trim().isEmpty(), "X12 content should not be empty");

            System.out.println("Generated X12 content: " + x12Content.length() + " characters");
            System.out.println("Content preview: " + x12Content.substring(0, Math.min(200, x12Content.length())));

            // Step 5: Validate using com.imsweb X12 parser
            validateX12Content(x12Content);

        } catch (Exception e) {
            System.err.println("Error generating bulk X12 claims: " + e.getMessage());
            e.printStackTrace();
            fail("Failed to generate bulk X12 claims: " + e.getMessage());
        }
    }



    /**
     * Validates X12 content using the com.imsweb X12 parser.
     * Reports detailed information about any validation errors found.
     */
    private void validateX12Content(String x12Content) throws IOException {
        try {
            // Write content to a temporary file since X12Reader expects a File
            File tempFile = File.createTempFile("x12_test", ".txt");
            tempFile.deleteOnExit();
            Files.write(tempFile.toPath(), x12Content.getBytes());

            // Use the constructor shown in the GitHub documentation
            X12Reader x12Reader = new X12Reader(X12Reader.FileType.ANSI837_5010_X222, tempFile);

            // Parse the X12 file
            List<Loop> loops = x12Reader.getLoops();

            // Check for any parsing errors
            List<String> errors = x12Reader.getErrors();
            List<String> fatalErrors = x12Reader.getFatalErrors();

            // Report what the parser found
            System.out.println("\n=== X12 Parser Results ===");
            System.out.println("Loops parsed: " + (loops != null ? loops.size() : 0));
            System.out.println("Errors: " + errors.size());
            System.out.println("Fatal errors: " + fatalErrors.size());

            if (!errors.isEmpty()) {
                System.out.println("\nErrors found:");
                for (int i = 0; i < errors.size(); i++) {
                    System.out.println((i + 1) + ". " + errors.get(i));
                }
            }

            if (!fatalErrors.isEmpty()) {
                System.out.println("\nFatal errors found:");
                for (int i = 0; i < fatalErrors.size(); i++) {
                    System.out.println((i + 1) + ". " + fatalErrors.get(i));
                }
            }

            // Success message if no errors
            if (errors.isEmpty() && fatalErrors.isEmpty()) {
                System.out.println("✅ X12 validation PASSED - No errors found!");
            }

            // Fail test if there are any errors
            if (!errors.isEmpty() || !fatalErrors.isEmpty()) {
                fail("X12 validation found " + errors.size() + " errors and " + fatalErrors.size() + " fatal errors.");
            }

        } catch (Exception e) {
            fail("Failed to parse X12 file: " + e.getMessage(), e);
        }
    }
}