package com.nymbl.config.x12.x837;

import com.imsweb.x12.Loop;
import com.imsweb.x12.reader.X12Reader;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive validation tests for bulk X12 claim generation using com.imsweb:x12-parser library.
 * This test validates that bulk X12 files are 100% valid according to professional X12 standards.
 *
 * This test can validate existing X12 files or generate new ones for testing.
 */
public class BulkX12ValidationTest {

    /**
     * Test that validates an existing X12 file using com.imsweb X12 parser.
     * This test expects an x12.txt file to exist in the project root.
     * Generate one manually via the UI first, then run this test to validate it.
     */
    @Test
    public void testValidateExistingX12File() throws IOException {
        // Check if x12.txt exists
        if (!Files.exists(Paths.get("x12.txt"))) {
            System.out.println("No existing x12.txt file found. Please generate a bulk claim via the UI first.");
            System.out.println("This test validates existing X12 files - it doesn't generate them.");
            return; // Skip test if no file exists
        }

        // Read the x12.txt file from project root
        String x12Content = Files.readString(Paths.get("x12.txt"));

        assertNotNull(x12Content, "X12 file content should not be null");
        assertFalse(x12Content.trim().isEmpty(), "X12 file content should not be empty");

        System.out.println("Validating existing X12 file: x12.txt");
        System.out.println("X12 file size: " + x12Content.length() + " characters");

        // Validate using com.imsweb X12 parser
        validateX12Content(x12Content);
    }



    /**
     * Validates X12 content using the com.imsweb X12 parser.
     * Reports detailed information about any validation errors found.
     */
    private void validateX12Content(String x12Content) throws IOException {
        try {
            // Write content to a temporary file since X12Reader expects a File
            File tempFile = File.createTempFile("x12_test", ".txt");
            tempFile.deleteOnExit();
            Files.write(tempFile.toPath(), x12Content.getBytes());

            // Use the constructor shown in the GitHub documentation
            X12Reader x12Reader = new X12Reader(X12Reader.FileType.ANSI837_5010_X222, tempFile);

            // Parse the X12 file
            List<Loop> loops = x12Reader.getLoops();

            // Check for any parsing errors
            List<String> errors = x12Reader.getErrors();
            List<String> fatalErrors = x12Reader.getFatalErrors();

            // Report what the parser found
            System.out.println("\n=== X12 Parser Results ===");
            System.out.println("Loops parsed: " + (loops != null ? loops.size() : 0));
            System.out.println("Errors: " + errors.size());
            System.out.println("Fatal errors: " + fatalErrors.size());

            if (!errors.isEmpty()) {
                System.out.println("\nErrors found:");
                for (int i = 0; i < errors.size(); i++) {
                    System.out.println((i + 1) + ". " + errors.get(i));
                }
            }

            if (!fatalErrors.isEmpty()) {
                System.out.println("\nFatal errors found:");
                for (int i = 0; i < fatalErrors.size(); i++) {
                    System.out.println((i + 1) + ". " + fatalErrors.get(i));
                }
            }

            // Success message if no errors
            if (errors.isEmpty() && fatalErrors.isEmpty()) {
                System.out.println("✅ X12 validation PASSED - No errors found!");
            }

            // Fail test if there are any errors
            if (!errors.isEmpty() || !fatalErrors.isEmpty()) {
                fail("X12 validation found " + errors.size() + " errors and " + fatalErrors.size() + " fatal errors.");
            }

        } catch (Exception e) {
            fail("Failed to parse X12 file: " + e.getMessage(), e);
        }
    }
}